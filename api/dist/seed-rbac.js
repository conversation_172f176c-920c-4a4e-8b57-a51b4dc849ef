"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const rbac_seeder_service_1 = require("./database/seeds/rbac-seeder.service");
async function seedRbac() {
    const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
    const rbacSeeder = app.get(rbac_seeder_service_1.RbacSeederService);
    try {
        await rbacSeeder.seedAll();
        console.log('RBAC seeding completed successfully!');
    }
    catch (error) {
        console.error('Error seeding RBAC:', error);
    }
    finally {
        await app.close();
    }
}
seedRbac();
//# sourceMappingURL=seed-rbac.js.map