import { CompaniesService } from './companies.service';
export declare class CompaniesController {
    private companiesService;
    constructor(companiesService: CompaniesService);
    findAll(): Promise<import("../../database").Company[]>;
    findOne(id: string): Promise<import("../../database").Company>;
    create(createCompanyDto: any): Promise<import("../../database").Company>;
    update(id: string, updateData: any): Promise<import("../../database").Company>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
