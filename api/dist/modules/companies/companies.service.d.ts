import { Repository } from 'typeorm';
import { Company } from '../../database/entities';
export declare class CompaniesService {
    private companyRepository;
    constructor(companyRepository: Repository<Company>);
    create(createCompanyDto: Partial<Company>): Promise<Company>;
    findAll(): Promise<Company[]>;
    findOne(id: number): Promise<Company>;
    update(id: number, updateData: Partial<Company>): Promise<Company>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
