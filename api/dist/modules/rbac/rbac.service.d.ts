import { Repository } from 'typeorm';
import { User, Role, Permission } from '../../database/entities';
export declare class RbacService {
    private userRepository;
    private roleRepository;
    private permissionRepository;
    constructor(userRepository: Repository<User>, roleRepository: Repository<Role>, permissionRepository: Repository<Permission>);
    getUserPermissions(userId: number): Promise<string[]>;
    hasPermission(userId: number, permissionName: string): Promise<boolean>;
    hasRole(userId: number, roleName: string): Promise<boolean>;
    getUserRole(userId: number): Promise<Role | null>;
    assignRole(userId: number, roleId: number): Promise<void>;
    createPermission(name: string, description: string, resource: string, action: string): Promise<Permission>;
    assignPermissionToRole(roleId: number, permissionId: number): Promise<void>;
    getRolePermissions(roleId: number): Promise<Permission[]>;
    getAllRoles(): Promise<Role[]>;
    getAllPermissions(): Promise<Permission[]>;
}
