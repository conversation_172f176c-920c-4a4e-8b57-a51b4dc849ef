import { RbacService } from './rbac.service';
export declare class Rbac<PERSON>ontroller {
    private readonly rbacService;
    constructor(rbacService: RbacService);
    getAllRoles(): Promise<import("../../database").Role[]>;
    getAllPermissions(): Promise<import("../../database").Permission[]>;
    getUserPermissions(userId: number): Promise<string[]>;
    getUserRole(userId: number): Promise<import("../../database").Role>;
    assignRole(userId: number, roleId: number): Promise<{
        message: string;
    }>;
    createPermission(createPermissionDto: {
        name: string;
        description: string;
        resource: string;
        action: string;
    }): Promise<import("../../database").Permission>;
    assignPermissionToRole(roleId: number, permissionId: number): Promise<{
        message: string;
    }>;
}
