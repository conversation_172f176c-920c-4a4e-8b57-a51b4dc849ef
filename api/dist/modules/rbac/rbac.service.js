"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RbacService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("../../database/entities");
let RbacService = class RbacService {
    constructor(userRepository, roleRepository, permissionRepository) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
    }
    async getUserPermissions(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['role', 'role.permissions'],
        });
        if (!user || !user.role) {
            return [];
        }
        return user.role.permissions.map((permission) => permission.name);
    }
    async hasPermission(userId, permissionName) {
        const permissions = await this.getUserPermissions(userId);
        return permissions.includes(permissionName);
    }
    async hasRole(userId, roleName) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['role'],
        });
        return user?.role?.name === roleName;
    }
    async getUserRole(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['role'],
        });
        return user?.role || null;
    }
    async assignRole(userId, roleId) {
        await this.userRepository.update(userId, { roleId });
    }
    async createPermission(name, description, resource, action) {
        const permission = this.permissionRepository.create({
            name,
            description,
            resource,
            action,
        });
        return this.permissionRepository.save(permission);
    }
    async assignPermissionToRole(roleId, permissionId) {
        const role = await this.roleRepository.findOne({
            where: { id: roleId },
            relations: ['permissions'],
        });
        const permission = await this.permissionRepository.findOne({
            where: { id: permissionId },
        });
        if (role && permission) {
            role.permissions.push(permission);
            await this.roleRepository.save(role);
        }
    }
    async getRolePermissions(roleId) {
        const role = await this.roleRepository.findOne({
            where: { id: roleId },
            relations: ['permissions'],
        });
        return role?.permissions || [];
    }
    async getAllRoles() {
        return this.roleRepository.find({
            relations: ['permissions'],
        });
    }
    async getAllPermissions() {
        return this.permissionRepository.find();
    }
};
exports.RbacService = RbacService;
exports.RbacService = RbacService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.Role)),
    __param(2, (0, typeorm_1.InjectRepository)(entities_1.Permission)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], RbacService);
//# sourceMappingURL=rbac.service.js.map