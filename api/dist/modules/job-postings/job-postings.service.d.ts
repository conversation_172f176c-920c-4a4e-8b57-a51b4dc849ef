import { Repository } from 'typeorm';
import { JobPosting } from '../../database/entities';
export declare class JobPostingsService {
    private jobPostingRepository;
    constructor(jobPostingRepository: Repository<JobPosting>);
    create(createJobPostingDto: Partial<JobPosting>): Promise<JobPosting>;
    findAll(filters?: any): Promise<JobPosting[]>;
    findOne(id: number): Promise<JobPosting>;
    update(id: number, updateData: Partial<JobPosting>): Promise<JobPosting>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
