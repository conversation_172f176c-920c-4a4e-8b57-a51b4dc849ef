import { JobPostingsService } from './job-postings.service';
export declare class JobPostingsController {
    private jobPostingsService;
    constructor(jobPostingsService: JobPostingsService);
    findAll(filters: any): Promise<import("../../database").JobPosting[]>;
    findOne(id: string): Promise<import("../../database").JobPosting>;
    create(createJobPostingDto: any): Promise<import("../../database").JobPosting>;
    update(id: string, updateData: any): Promise<import("../../database").JobPosting>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
