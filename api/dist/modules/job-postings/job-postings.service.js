"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobPostingsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("../../database/entities");
let JobPostingsService = class JobPostingsService {
    constructor(jobPostingRepository) {
        this.jobPostingRepository = jobPostingRepository;
    }
    async create(createJobPostingDto) {
        const jobPosting = this.jobPostingRepository.create(createJobPostingDto);
        return this.jobPostingRepository.save(jobPosting);
    }
    async findAll(filters) {
        const query = this.jobPostingRepository
            .createQueryBuilder('job')
            .leftJoinAndSelect('job.company', 'company')
            .where('job.status = :status', { status: 'active' });
        if (filters?.location) {
            query.andWhere('job.location ILIKE :location', {
                location: `%${filters.location}%`,
            });
        }
        if (filters?.jobType) {
            query.andWhere('job.jobType = :jobType', { jobType: filters.jobType });
        }
        if (filters?.companyId) {
            query.andWhere('job.companyId = :companyId', {
                companyId: filters.companyId,
            });
        }
        return query.getMany();
    }
    async findOne(id) {
        return this.jobPostingRepository.findOne({
            where: { id },
            relations: ['company', 'applications'],
        });
    }
    async update(id, updateData) {
        await this.jobPostingRepository.update(id, updateData);
        return this.findOne(id);
    }
    async remove(id) {
        await this.jobPostingRepository.update(id, { status: 'closed' });
        return { message: 'Job posting closed successfully' };
    }
};
exports.JobPostingsService = JobPostingsService;
exports.JobPostingsService = JobPostingsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.JobPosting)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], JobPostingsService);
//# sourceMappingURL=job-postings.service.js.map