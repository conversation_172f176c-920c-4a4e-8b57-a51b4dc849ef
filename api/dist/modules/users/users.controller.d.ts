import { UsersService } from './users.service';
export declare class UsersController {
    private usersService;
    constructor(usersService: UsersService);
    findAll(): Promise<import("../../database").User[]>;
    findOne(id: string): Promise<import("../../database").User>;
    update(id: string, updateData: any): Promise<import("../../database").User>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
