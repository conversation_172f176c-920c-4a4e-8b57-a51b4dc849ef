"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RbacSeederService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("../entities");
const permissions_1 = require("../../modules/rbac/constants/permissions");
let RbacSeederService = class RbacSeederService {
    constructor(roleRepository, permissionRepository) {
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
    }
    async seedRoles() {
        const roleDescriptions = {
            [permissions_1.ROLES.JOB_SEEKER]: 'Người tìm việc - có thể tìm kiếm và ứng tuyển công việc',
            [permissions_1.ROLES.RECRUITER]: 'Nhà tuyển dụng - có thể đăng tin và quản lý tuyển dụng',
            [permissions_1.ROLES.SUPER_ADMIN]: 'Quản trị viên hệ thống - có toàn quyền quản lý',
        };
        for (const [roleName, description] of Object.entries(roleDescriptions)) {
            const existingRole = await this.roleRepository.findOne({
                where: { name: roleName },
            });
            if (!existingRole) {
                const role = this.roleRepository.create({
                    name: roleName,
                    description,
                });
                await this.roleRepository.save(role);
                console.log(`Created role: ${roleName}`);
            }
        }
    }
    async seedPermissions() {
        const permissionData = [
            {
                name: permissions_1.PERMISSIONS.CREATE_USER,
                resource: 'user',
                action: 'create',
                description: 'Tạo user mới',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_USER,
                resource: 'user',
                action: 'view',
                description: 'Xem thông tin user',
            },
            {
                name: permissions_1.PERMISSIONS.UPDATE_USER,
                resource: 'user',
                action: 'update',
                description: 'Cập nhật thông tin user',
            },
            {
                name: permissions_1.PERMISSIONS.DELETE_USER,
                resource: 'user',
                action: 'delete',
                description: 'Xóa user',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_ALL_USERS,
                resource: 'user',
                action: 'view_all',
                description: 'Xem tất cả users',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_USER_ROLE,
                resource: 'role',
                action: 'view',
                description: 'Xem role của user',
            },
            {
                name: permissions_1.PERMISSIONS.ASSIGN_USER_ROLE,
                resource: 'role',
                action: 'assign',
                description: 'Gán role cho user',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_USER_PERMISSIONS,
                resource: 'permission',
                action: 'view',
                description: 'Xem permissions của user',
            },
            {
                name: permissions_1.PERMISSIONS.MANAGE_ROLES,
                resource: 'role',
                action: 'manage',
                description: 'Quản lý roles',
            },
            {
                name: permissions_1.PERMISSIONS.MANAGE_PERMISSIONS,
                resource: 'permission',
                action: 'manage',
                description: 'Quản lý permissions',
            },
            {
                name: permissions_1.PERMISSIONS.CREATE_COMPANY,
                resource: 'company',
                action: 'create',
                description: 'Tạo công ty mới',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_COMPANY,
                resource: 'company',
                action: 'view',
                description: 'Xem thông tin công ty',
            },
            {
                name: permissions_1.PERMISSIONS.UPDATE_COMPANY,
                resource: 'company',
                action: 'update',
                description: 'Cập nhật thông tin công ty',
            },
            {
                name: permissions_1.PERMISSIONS.DELETE_COMPANY,
                resource: 'company',
                action: 'delete',
                description: 'Xóa công ty',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_ALL_COMPANIES,
                resource: 'company',
                action: 'view_all',
                description: 'Xem tất cả công ty',
            },
            {
                name: permissions_1.PERMISSIONS.MANAGE_OWN_COMPANY,
                resource: 'company',
                action: 'manage_own',
                description: 'Quản lý công ty của mình',
            },
            {
                name: permissions_1.PERMISSIONS.CREATE_JOB_POSTING,
                resource: 'job',
                action: 'create',
                description: 'Đăng tin tuyển dụng',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_JOB_POSTING,
                resource: 'job',
                action: 'view',
                description: 'Xem tin tuyển dụng',
            },
            {
                name: permissions_1.PERMISSIONS.UPDATE_JOB_POSTING,
                resource: 'job',
                action: 'update',
                description: 'Cập nhật tin tuyển dụng',
            },
            {
                name: permissions_1.PERMISSIONS.DELETE_JOB_POSTING,
                resource: 'job',
                action: 'delete',
                description: 'Xóa tin tuyển dụng',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_ALL_JOB_POSTINGS,
                resource: 'job',
                action: 'view_all',
                description: 'Xem tất cả tin tuyển dụng',
            },
            {
                name: permissions_1.PERMISSIONS.MANAGE_OWN_JOB_POSTINGS,
                resource: 'job',
                action: 'manage_own',
                description: 'Quản lý tin tuyển dụng của mình',
            },
            {
                name: permissions_1.PERMISSIONS.SUBMIT_APPLICATION,
                resource: 'application',
                action: 'submit',
                description: 'Nộp đơn ứng tuyển',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_APPLICATION,
                resource: 'application',
                action: 'view',
                description: 'Xem đơn ứng tuyển',
            },
            {
                name: permissions_1.PERMISSIONS.UPDATE_APPLICATION,
                resource: 'application',
                action: 'update',
                description: 'Cập nhật đơn ứng tuyển',
            },
            {
                name: permissions_1.PERMISSIONS.DELETE_APPLICATION,
                resource: 'application',
                action: 'delete',
                description: 'Xóa đơn ứng tuyển',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_ALL_APPLICATIONS,
                resource: 'application',
                action: 'view_all',
                description: 'Xem tất cả đơn ứng tuyển',
            },
            {
                name: permissions_1.PERMISSIONS.REVIEW_APPLICATIONS,
                resource: 'application',
                action: 'review',
                description: 'Đánh giá đơn ứng tuyển',
            },
            {
                name: permissions_1.PERMISSIONS.CREATE_RESUME,
                resource: 'resume',
                action: 'create',
                description: 'Tạo CV',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_RESUME,
                resource: 'resume',
                action: 'view',
                description: 'Xem CV',
            },
            {
                name: permissions_1.PERMISSIONS.UPDATE_RESUME,
                resource: 'resume',
                action: 'update',
                description: 'Cập nhật CV',
            },
            {
                name: permissions_1.PERMISSIONS.DELETE_RESUME,
                resource: 'resume',
                action: 'delete',
                description: 'Xóa CV',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_ALL_RESUMES,
                resource: 'resume',
                action: 'view_all',
                description: 'Xem tất cả CV',
            },
            {
                name: permissions_1.PERMISSIONS.SCHEDULE_INTERVIEW,
                resource: 'interview',
                action: 'schedule',
                description: 'Lên lịch phỏng vấn',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_INTERVIEW,
                resource: 'interview',
                action: 'view',
                description: 'Xem lịch phỏng vấn',
            },
            {
                name: permissions_1.PERMISSIONS.UPDATE_INTERVIEW,
                resource: 'interview',
                action: 'update',
                description: 'Cập nhật lịch phỏng vấn',
            },
            {
                name: permissions_1.PERMISSIONS.DELETE_INTERVIEW,
                resource: 'interview',
                action: 'delete',
                description: 'Xóa lịch phỏng vấn',
            },
            {
                name: permissions_1.PERMISSIONS.CONDUCT_INTERVIEW,
                resource: 'interview',
                action: 'conduct',
                description: 'Tiến hành phỏng vấn',
            },
            {
                name: permissions_1.PERMISSIONS.SEND_NOTIFICATION,
                resource: 'notification',
                action: 'send',
                description: 'Gửi thông báo',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_NOTIFICATION,
                resource: 'notification',
                action: 'view',
                description: 'Xem thông báo',
            },
            {
                name: permissions_1.PERMISSIONS.MANAGE_NOTIFICATIONS,
                resource: 'notification',
                action: 'manage',
                description: 'Quản lý thông báo',
            },
            {
                name: permissions_1.PERMISSIONS.SAVE_JOB,
                resource: 'saved_job',
                action: 'save',
                description: 'Lưu công việc',
            },
            {
                name: permissions_1.PERMISSIONS.FOLLOW_COMPANY,
                resource: 'company_follow',
                action: 'follow',
                description: 'Theo dõi công ty',
            },
            {
                name: permissions_1.PERMISSIONS.SYSTEM_ADMIN,
                resource: 'system',
                action: 'admin',
                description: 'Quản trị hệ thống',
            },
            {
                name: permissions_1.PERMISSIONS.VIEW_ANALYTICS,
                resource: 'analytics',
                action: 'view',
                description: 'Xem thống kê',
            },
            {
                name: permissions_1.PERMISSIONS.MANAGE_SYSTEM_SETTINGS,
                resource: 'system',
                action: 'manage_settings',
                description: 'Quản lý cài đặt hệ thống',
            },
        ];
        for (const permData of permissionData) {
            const existingPermission = await this.permissionRepository.findOne({
                where: { name: permData.name },
            });
            if (!existingPermission) {
                const permission = this.permissionRepository.create(permData);
                await this.permissionRepository.save(permission);
                console.log(`Created permission: ${permData.name}`);
            }
        }
    }
    async assignPermissionsToRoles() {
        for (const [roleName, permissionNames] of Object.entries(permissions_1.ROLE_PERMISSIONS)) {
            const role = await this.roleRepository.findOne({
                where: { name: roleName },
                relations: ['permissions'],
            });
            if (role) {
                const permissions = await this.permissionRepository.find({
                    where: permissionNames.map((name) => ({ name })),
                });
                role.permissions = permissions;
                await this.roleRepository.save(role);
                console.log(`Assigned ${permissions.length} permissions to role: ${roleName}`);
            }
        }
    }
    async seedAll() {
        console.log('🌱 Starting RBAC seeding...');
        await this.seedRoles();
        await this.seedPermissions();
        await this.assignPermissionsToRoles();
        console.log('✅ RBAC seeding completed!');
    }
};
exports.RbacSeederService = RbacSeederService;
exports.RbacSeederService = RbacSeederService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.Role)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.Permission)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], RbacSeederService);
//# sourceMappingURL=rbac-seeder.service.js.map