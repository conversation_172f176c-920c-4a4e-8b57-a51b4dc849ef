import { Repository } from 'typeorm';
import { Role, Permission } from '../entities';
export declare class RbacSeederService {
    private roleRepository;
    private permissionRepository;
    constructor(roleRepository: Repository<Role>, permissionRepository: Repository<Permission>);
    seedRoles(): Promise<void>;
    seedPermissions(): Promise<void>;
    assignPermissionsToRoles(): Promise<void>;
    seedAll(): Promise<void>;
}
