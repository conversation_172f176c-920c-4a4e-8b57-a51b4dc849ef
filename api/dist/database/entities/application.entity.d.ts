import { JobPosting } from './job-posting.entity';
import { Resume } from './resume.entity';
import { User } from './user.entity';
export declare class Application {
    id: number;
    jobPostingId: number;
    resumeId: number;
    userId: number;
    status: string;
    coverLetter: string;
    appliedAt: Date;
    createdAt: Date;
    updatedAt: Date;
    jobPosting: JobPosting;
    resume: Resume;
    user: User;
    interviews: any[];
}
