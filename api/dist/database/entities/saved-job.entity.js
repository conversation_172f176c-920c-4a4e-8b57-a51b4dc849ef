"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SavedJob = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./user.entity");
const job_posting_entity_1 = require("./job-posting.entity");
let SavedJob = class SavedJob {
};
exports.SavedJob = SavedJob;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SavedJob.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", Number)
], SavedJob.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'job_posting_id' }),
    __metadata("design:type", Number)
], SavedJob.prototype, "jobPostingId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'saved_at',
        type: 'timestamp',
        default: () => 'CURRENT_TIMESTAMP',
    }),
    __metadata("design:type", Date)
], SavedJob.prototype, "savedAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], SavedJob.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], SavedJob.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.savedJobs),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], SavedJob.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => job_posting_entity_1.JobPosting, (jobPosting) => jobPosting.savedByUsers),
    (0, typeorm_1.JoinColumn)({ name: 'job_posting_id' }),
    __metadata("design:type", job_posting_entity_1.JobPosting)
], SavedJob.prototype, "jobPosting", void 0);
exports.SavedJob = SavedJob = __decorate([
    (0, typeorm_1.Entity)('saved_job')
], SavedJob);
//# sourceMappingURL=saved-job.entity.js.map