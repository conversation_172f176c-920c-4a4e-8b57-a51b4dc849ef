import { Role } from './role.entity';
export declare class User {
    id: number;
    roleId: number;
    username: string;
    passwordHash: string;
    fullName: string;
    email: string;
    phoneNumber: string;
    address: string;
    avatarUrl: string;
    accountCreatedAt: Date;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    role: Role;
    resumes: any[];
    applications: any[];
    notifications: any[];
    savedJobs: any[];
    companyFollows: any[];
}
