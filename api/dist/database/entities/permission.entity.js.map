{"version": 3, "file": "permission.entity.js", "sourceRoot": "", "sources": ["../../../src/database/entities/permission.entity.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAOiB;AACjB,+CAAqC;AAG9B,IAAM,UAAU,GAAhB,MAAM,UAAU;CAwBtB,CAAA;AAxBY,gCAAU;AAErB;IADC,IAAA,gCAAsB,GAAE;;sCACd;AAGX;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;wCACzB;AAGb;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;+CACpB;AAGpB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;;4CACP;AAGjB;IADC,IAAA,gBAAM,EAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;;0CACR;AAGf;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BAC9B,IAAI;6CAAC;AAGhB;IADC,IAAA,0BAAgB,EAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;8BAC9B,IAAI;6CAAC;AAGhB;IADC,IAAA,oBAAU,EAAC,GAAG,EAAE,CAAC,kBAAI,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC;;yCACrC;qBAvBH,UAAU;IADtB,IAAA,gBAAM,EAAC,YAAY,CAAC;GACR,UAAU,CAwBtB"}