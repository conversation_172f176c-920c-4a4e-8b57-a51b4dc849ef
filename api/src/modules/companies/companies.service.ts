import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Company } from '../../database/entities';

@Injectable()
export class CompaniesService {
  constructor(
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
  ) {}

  async create(createCompanyDto: Partial<Company>) {
    const company = this.companyRepository.create(createCompanyDto);
    return this.companyRepository.save(company);
  }

  async findAll() {
    return this.companyRepository.find({
      where: { isActive: true },
    });
  }

  async findOne(id: number) {
    return this.companyRepository.findOne({
      where: { id, isActive: true },
      relations: ['jobPostings'],
    });
  }

  async update(id: number, updateData: Partial<Company>) {
    await this.companyRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: number) {
    await this.companyRepository.update(id, { isActive: false });
    return { message: 'Company deactivated successfully' };
  }
}
