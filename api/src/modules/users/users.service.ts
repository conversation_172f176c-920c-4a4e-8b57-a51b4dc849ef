import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../database/entities';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async findAll() {
    return this.userRepository.find({
      relations: ['role'],
      select: [
        'id',
        'username',
        'fullName',
        'email',
        'phoneNumber',
        'isActive',
        'createdAt',
      ],
    });
  }

  async findOne(id: number) {
    return this.userRepository.findOne({
      where: { id },
      relations: ['role'],
      select: [
        'id',
        'username',
        'fullName',
        'email',
        'phoneNumber',
        'address',
        'avatarUrl',
        'isActive',
        'createdAt',
      ],
    });
  }

  async update(id: number, updateData: Partial<User>) {
    await this.userRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: number) {
    await this.userRepository.update(id, { isActive: false });
    return { message: 'User deactivated successfully' };
  }
}
