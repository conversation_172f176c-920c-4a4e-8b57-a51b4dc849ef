import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobPosting } from '../../database/entities';

@Injectable()
export class JobPostingsService {
  constructor(
    @InjectRepository(JobPosting)
    private jobPostingRepository: Repository<JobPosting>,
  ) {}

  async create(createJobPostingDto: Partial<JobPosting>) {
    const jobPosting = this.jobPostingRepository.create(createJobPostingDto);
    return this.jobPostingRepository.save(jobPosting);
  }

  async findAll(filters?: any) {
    const query = this.jobPostingRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.company', 'company')
      .where('job.status = :status', { status: 'active' });

    if (filters?.location) {
      query.andWhere('job.location ILIKE :location', {
        location: `%${filters.location}%`,
      });
    }

    if (filters?.jobType) {
      query.andWhere('job.jobType = :jobType', { jobType: filters.jobType });
    }

    if (filters?.companyId) {
      query.andWhere('job.companyId = :companyId', {
        companyId: filters.companyId,
      });
    }

    return query.getMany();
  }

  async findOne(id: number) {
    return this.jobPostingRepository.findOne({
      where: { id },
      relations: ['company', 'applications'],
    });
  }

  async update(id: number, updateData: Partial<JobPosting>) {
    await this.jobPostingRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: number) {
    await this.jobPostingRepository.update(id, { status: 'closed' });
    return { message: 'Job posting closed successfully' };
  }
}
