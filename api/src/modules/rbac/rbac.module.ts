import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User, Role, Permission } from '../../database/entities';
import { RbacService } from './rbac.service';
import { <PERSON>bacController } from './rbac.controller';
import { RbacGuard } from './guards/rbac.guard';
import { RbacSeederService } from '../../database/seeds/rbac-seeder.service';

@Module({
  imports: [TypeOrmModule.forFeature([User, Role, Permission])],
  providers: [RbacService, RbacGuard, RbacSeederService],
  controllers: [RbacController],
  exports: [RbacService, RbacGuard, RbacSeederService],
})
export class RbacModule {}
