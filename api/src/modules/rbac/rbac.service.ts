import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, Role, Permission } from '../../database/entities';

@Injectable()
export class RbacService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
  ) {}

  /**
   * <PERSON><PERSON>y tất cả permissions của user thông qua role
   */
  async getUserPermissions(userId: number): Promise<string[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role', 'role.permissions'],
    });

    if (!user || !user.role) {
      return [];
    }

    return user.role.permissions.map((permission) => permission.name);
  }

  /**
   * <PERSON><PERSON><PERSON> tra user có permission không
   */
  async hasPermission(
    userId: number,
    permissionName: string,
  ): Promise<boolean> {
    const permissions = await this.getUserPermissions(userId);
    return permissions.includes(permissionName);
  }

  /**
   * Kiểm tra user có role không
   */
  async hasRole(userId: number, roleName: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role'],
    });

    return user?.role?.name === roleName;
  }

  /**
   * Lấy role của user
   */
  async getUserRole(userId: number): Promise<Role | null> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role'],
    });

    return user?.role || null;
  }

  /**
   * Gán role cho user
   */
  async assignRole(userId: number, roleId: number): Promise<void> {
    await this.userRepository.update(userId, { roleId });
  }

  /**
   * Tạo permission mới
   */
  async createPermission(
    name: string,
    description: string,
    resource: string,
    action: string,
  ): Promise<Permission> {
    const permission = this.permissionRepository.create({
      name,
      description,
      resource,
      action,
    });

    return this.permissionRepository.save(permission);
  }

  /**
   * Gán permission cho role
   */
  async assignPermissionToRole(
    roleId: number,
    permissionId: number,
  ): Promise<void> {
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
      relations: ['permissions'],
    });

    const permission = await this.permissionRepository.findOne({
      where: { id: permissionId },
    });

    if (role && permission) {
      role.permissions.push(permission);
      await this.roleRepository.save(role);
    }
  }

  /**
   * Lấy tất cả permissions của role
   */
  async getRolePermissions(roleId: number): Promise<Permission[]> {
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
      relations: ['permissions'],
    });

    return role?.permissions || [];
  }

  /**
   * Lấy tất cả roles
   */
  async getAllRoles(): Promise<Role[]> {
    return this.roleRepository.find({
      relations: ['permissions'],
    });
  }

  /**
   * Lấy tất cả permissions
   */
  async getAllPermissions(): Promise<Permission[]> {
    return this.permissionRepository.find();
  }
}
