import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { RbacSeederService } from './database/seeds/rbac-seeder.service';

async function seedRbac() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const rbacSeeder = app.get(RbacSeederService);

  try {
    await rbacSeeder.seedAll();
    console.log('RBAC seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding RBAC:', error);
  } finally {
    await app.close();
  }
}

seedRbac();
