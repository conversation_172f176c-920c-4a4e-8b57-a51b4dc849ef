import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Company } from './company.entity';

@Entity('job_posting')
export class JobPosting {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'company_id' })
  companyId: number;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ length: 255, nullable: true })
  location: string;

  @Column({
    name: 'salary_min',
    type: 'decimal',
    precision: 12,
    scale: 2,
    nullable: true,
  })
  salaryMin: number;

  @Column({
    name: 'salary_max',
    type: 'decimal',
    precision: 12,
    scale: 2,
    nullable: true,
  })
  salaryMax: number;

  @Column({ name: 'job_type', length: 50, nullable: true })
  jobType: string; // full_time, part_time, contract, internship

  @Column({ length: 50, default: 'active' })
  status: string; // active, paused, closed, draft

  @Column({
    name: 'posted_at',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  postedAt: Date;

  @Column({ name: 'expires_at', type: 'timestamp', nullable: true })
  expiresAt: Date;

  @Column({ name: 'vacancy_count', default: 1 })
  vacancyCount: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => Company, (company) => company.jobPostings)
  @JoinColumn({ name: 'company_id' })
  company: Company;

  @OneToMany('Application', 'jobPosting')
  applications: any[];

  @OneToMany('SavedJob', 'jobPosting')
  savedByUsers: any[];
}
