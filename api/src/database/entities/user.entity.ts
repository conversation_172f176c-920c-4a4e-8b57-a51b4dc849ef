import { <PERSON><PERSON><PERSON>, Column, ManyTo<PERSON>ne, OneToMany, Jo<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Role } from './role.entity';

@Entity('user')
export class User extends BaseEntity {
  id: number;

  @Column({ name: 'role_id' })
  roleId: number;

  @Column({ length: 50, unique: true })
  username: string;

  @Column({ name: 'password_hash', length: 255 })
  passwordHash: string;

  @Column({ name: 'full_name', length: 255 })
  fullName: string;

  @Column({ length: 100, unique: true })
  email: string;

  @Column({ name: 'phone_number', length: 20, nullable: true })
  phoneNumber: string;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ name: 'avatar_url', length: 500, nullable: true })
  avatarUrl: string;

  @Column({
    name: 'account_created_at',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  accountCreatedAt: Date;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @ManyToOne(() => Role, (role) => role.users)
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @OneToMany('Resume', 'user')
  resumes: any[];

  @OneToMany('Application', 'user')
  applications: any[];

  @OneToMany('Notification', 'recipient')
  notifications: any[];

  @OneToMany('SavedJob', 'user')
  savedJobs: any[];

  @OneToMany('CompanyFollow', 'user')
  companyFollows: any[];
}
