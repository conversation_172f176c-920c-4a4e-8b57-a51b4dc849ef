import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';
import { JobPosting } from './job-posting.entity';

@Entity('saved_job')
export class SavedJob {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'job_posting_id' })
  jobPostingId: number;

  @Column({
    name: 'saved_at',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  savedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => User, (user) => user.savedJobs)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => JobPosting, (jobPosting) => jobPosting.savedByUsers)
  @JoinColumn({ name: 'job_posting_id' })
  jobPosting: JobPosting;
}
