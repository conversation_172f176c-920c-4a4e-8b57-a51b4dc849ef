import { Entity, Column, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';

@Entity('company')
export class Company extends BaseEntity {
  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'logo_url', length: 500, nullable: true })
  logoUrl: string;

  @Column({ length: 100, nullable: true })
  industry: string;

  @Column({ name: 'website_url', length: 255, nullable: true })
  websiteUrl: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @OneToMany('JobPosting', 'company')
  jobPostings: any[];

  @OneToMany('CompanyFollow', 'company')
  followers: any[];
}
