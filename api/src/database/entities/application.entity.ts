import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { JobPosting } from './job-posting.entity';
import { Resume } from './resume.entity';
import { User } from './user.entity';

@Entity('application')
export class Application {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'job_posting_id' })
  jobPostingId: number;

  @Column({ name: 'resume_id' })
  resumeId: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ length: 50, default: 'pending' })
  status: string; // pending, reviewing, shortlisted, interviewed, rejected, accepted

  @Column({ name: 'cover_letter', type: 'text', nullable: true })
  coverLetter: string;

  @Column({
    name: 'applied_at',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  appliedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => JobPosting, (jobPosting) => jobPosting.applications)
  @JoinColumn({ name: 'job_posting_id' })
  jobPosting: JobPosting;

  @ManyToOne(() => Resume, (resume) => resume.applications)
  @JoinColumn({ name: 'resume_id' })
  resume: Resume;

  @ManyToOne(() => User, (user) => user.applications)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany('Interview', 'application')
  interviews: any[];
}
