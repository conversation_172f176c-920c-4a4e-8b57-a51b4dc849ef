import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role, Permission } from '../entities';
import {
  PERMISSIONS,
  ROLES,
  ROLE_PERMISSIONS,
} from '../../modules/rbac/constants/permissions';

@Injectable()
export class RbacSeederService {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
  ) {}

  async seedRoles(): Promise<void> {
    const roleDescriptions = {
      [ROLES.JOB_SEEKER]:
        'Người tìm việc - có thể tìm kiếm và ứng tuyển công việc',
      [ROLES.RECRUITER]:
        'Nhà tuyển dụng - có thể đăng tin và quản lý tuyển dụng',
      [ROLES.SUPER_ADMIN]: 'Quản trị viên hệ thống - có toàn quyền quản lý',
    };

    for (const [roleName, description] of Object.entries(roleDescriptions)) {
      const existingRole = await this.roleRepository.findOne({
        where: { name: roleName },
      });

      if (!existingRole) {
        const role = this.roleRepository.create({
          name: roleName,
          description,
        });
        await this.roleRepository.save(role);
        console.log(`Created role: ${roleName}`);
      }
    }
  }

  async seedPermissions(): Promise<void> {
    const permissionData = [
      // User Management
      {
        name: PERMISSIONS.CREATE_USER,
        resource: 'user',
        action: 'create',
        description: 'Tạo user mới',
      },
      {
        name: PERMISSIONS.VIEW_USER,
        resource: 'user',
        action: 'view',
        description: 'Xem thông tin user',
      },
      {
        name: PERMISSIONS.UPDATE_USER,
        resource: 'user',
        action: 'update',
        description: 'Cập nhật thông tin user',
      },
      {
        name: PERMISSIONS.DELETE_USER,
        resource: 'user',
        action: 'delete',
        description: 'Xóa user',
      },
      {
        name: PERMISSIONS.VIEW_ALL_USERS,
        resource: 'user',
        action: 'view_all',
        description: 'Xem tất cả users',
      },

      // Role & Permission Management
      {
        name: PERMISSIONS.VIEW_USER_ROLE,
        resource: 'role',
        action: 'view',
        description: 'Xem role của user',
      },
      {
        name: PERMISSIONS.ASSIGN_USER_ROLE,
        resource: 'role',
        action: 'assign',
        description: 'Gán role cho user',
      },
      {
        name: PERMISSIONS.VIEW_USER_PERMISSIONS,
        resource: 'permission',
        action: 'view',
        description: 'Xem permissions của user',
      },
      {
        name: PERMISSIONS.MANAGE_ROLES,
        resource: 'role',
        action: 'manage',
        description: 'Quản lý roles',
      },
      {
        name: PERMISSIONS.MANAGE_PERMISSIONS,
        resource: 'permission',
        action: 'manage',
        description: 'Quản lý permissions',
      },

      // Company Management
      {
        name: PERMISSIONS.CREATE_COMPANY,
        resource: 'company',
        action: 'create',
        description: 'Tạo công ty mới',
      },
      {
        name: PERMISSIONS.VIEW_COMPANY,
        resource: 'company',
        action: 'view',
        description: 'Xem thông tin công ty',
      },
      {
        name: PERMISSIONS.UPDATE_COMPANY,
        resource: 'company',
        action: 'update',
        description: 'Cập nhật thông tin công ty',
      },
      {
        name: PERMISSIONS.DELETE_COMPANY,
        resource: 'company',
        action: 'delete',
        description: 'Xóa công ty',
      },
      {
        name: PERMISSIONS.VIEW_ALL_COMPANIES,
        resource: 'company',
        action: 'view_all',
        description: 'Xem tất cả công ty',
      },
      {
        name: PERMISSIONS.MANAGE_OWN_COMPANY,
        resource: 'company',
        action: 'manage_own',
        description: 'Quản lý công ty của mình',
      },

      // Job Posting Management
      {
        name: PERMISSIONS.CREATE_JOB_POSTING,
        resource: 'job',
        action: 'create',
        description: 'Đăng tin tuyển dụng',
      },
      {
        name: PERMISSIONS.VIEW_JOB_POSTING,
        resource: 'job',
        action: 'view',
        description: 'Xem tin tuyển dụng',
      },
      {
        name: PERMISSIONS.UPDATE_JOB_POSTING,
        resource: 'job',
        action: 'update',
        description: 'Cập nhật tin tuyển dụng',
      },
      {
        name: PERMISSIONS.DELETE_JOB_POSTING,
        resource: 'job',
        action: 'delete',
        description: 'Xóa tin tuyển dụng',
      },
      {
        name: PERMISSIONS.VIEW_ALL_JOB_POSTINGS,
        resource: 'job',
        action: 'view_all',
        description: 'Xem tất cả tin tuyển dụng',
      },
      {
        name: PERMISSIONS.MANAGE_OWN_JOB_POSTINGS,
        resource: 'job',
        action: 'manage_own',
        description: 'Quản lý tin tuyển dụng của mình',
      },

      // Application Management
      {
        name: PERMISSIONS.SUBMIT_APPLICATION,
        resource: 'application',
        action: 'submit',
        description: 'Nộp đơn ứng tuyển',
      },
      {
        name: PERMISSIONS.VIEW_APPLICATION,
        resource: 'application',
        action: 'view',
        description: 'Xem đơn ứng tuyển',
      },
      {
        name: PERMISSIONS.UPDATE_APPLICATION,
        resource: 'application',
        action: 'update',
        description: 'Cập nhật đơn ứng tuyển',
      },
      {
        name: PERMISSIONS.DELETE_APPLICATION,
        resource: 'application',
        action: 'delete',
        description: 'Xóa đơn ứng tuyển',
      },
      {
        name: PERMISSIONS.VIEW_ALL_APPLICATIONS,
        resource: 'application',
        action: 'view_all',
        description: 'Xem tất cả đơn ứng tuyển',
      },
      {
        name: PERMISSIONS.REVIEW_APPLICATIONS,
        resource: 'application',
        action: 'review',
        description: 'Đánh giá đơn ứng tuyển',
      },

      // Resume Management
      {
        name: PERMISSIONS.CREATE_RESUME,
        resource: 'resume',
        action: 'create',
        description: 'Tạo CV',
      },
      {
        name: PERMISSIONS.VIEW_RESUME,
        resource: 'resume',
        action: 'view',
        description: 'Xem CV',
      },
      {
        name: PERMISSIONS.UPDATE_RESUME,
        resource: 'resume',
        action: 'update',
        description: 'Cập nhật CV',
      },
      {
        name: PERMISSIONS.DELETE_RESUME,
        resource: 'resume',
        action: 'delete',
        description: 'Xóa CV',
      },
      {
        name: PERMISSIONS.VIEW_ALL_RESUMES,
        resource: 'resume',
        action: 'view_all',
        description: 'Xem tất cả CV',
      },

      // Interview Management
      {
        name: PERMISSIONS.SCHEDULE_INTERVIEW,
        resource: 'interview',
        action: 'schedule',
        description: 'Lên lịch phỏng vấn',
      },
      {
        name: PERMISSIONS.VIEW_INTERVIEW,
        resource: 'interview',
        action: 'view',
        description: 'Xem lịch phỏng vấn',
      },
      {
        name: PERMISSIONS.UPDATE_INTERVIEW,
        resource: 'interview',
        action: 'update',
        description: 'Cập nhật lịch phỏng vấn',
      },
      {
        name: PERMISSIONS.DELETE_INTERVIEW,
        resource: 'interview',
        action: 'delete',
        description: 'Xóa lịch phỏng vấn',
      },
      {
        name: PERMISSIONS.CONDUCT_INTERVIEW,
        resource: 'interview',
        action: 'conduct',
        description: 'Tiến hành phỏng vấn',
      },

      // Notification Management
      {
        name: PERMISSIONS.SEND_NOTIFICATION,
        resource: 'notification',
        action: 'send',
        description: 'Gửi thông báo',
      },
      {
        name: PERMISSIONS.VIEW_NOTIFICATION,
        resource: 'notification',
        action: 'view',
        description: 'Xem thông báo',
      },
      {
        name: PERMISSIONS.MANAGE_NOTIFICATIONS,
        resource: 'notification',
        action: 'manage',
        description: 'Quản lý thông báo',
      },

      // Saved Jobs & Following
      {
        name: PERMISSIONS.SAVE_JOB,
        resource: 'saved_job',
        action: 'save',
        description: 'Lưu công việc',
      },
      {
        name: PERMISSIONS.FOLLOW_COMPANY,
        resource: 'company_follow',
        action: 'follow',
        description: 'Theo dõi công ty',
      },

      // System Administration
      {
        name: PERMISSIONS.SYSTEM_ADMIN,
        resource: 'system',
        action: 'admin',
        description: 'Quản trị hệ thống',
      },
      {
        name: PERMISSIONS.VIEW_ANALYTICS,
        resource: 'analytics',
        action: 'view',
        description: 'Xem thống kê',
      },
      {
        name: PERMISSIONS.MANAGE_SYSTEM_SETTINGS,
        resource: 'system',
        action: 'manage_settings',
        description: 'Quản lý cài đặt hệ thống',
      },
    ];

    for (const permData of permissionData) {
      const existingPermission = await this.permissionRepository.findOne({
        where: { name: permData.name },
      });

      if (!existingPermission) {
        const permission = this.permissionRepository.create(permData);
        await this.permissionRepository.save(permission);
        console.log(`Created permission: ${permData.name}`);
      }
    }
  }

  async assignPermissionsToRoles(): Promise<void> {
    for (const [roleName, permissionNames] of Object.entries(
      ROLE_PERMISSIONS,
    )) {
      const role = await this.roleRepository.findOne({
        where: { name: roleName },
        relations: ['permissions'],
      });

      if (role) {
        const permissions = await this.permissionRepository.find({
          where: permissionNames.map((name) => ({ name })),
        });

        role.permissions = permissions;
        await this.roleRepository.save(role);
        console.log(
          `Assigned ${permissions.length} permissions to role: ${roleName}`,
        );
      }
    }
  }

  async seedAll(): Promise<void> {
    console.log('🌱 Starting RBAC seeding...');

    await this.seedRoles();
    await this.seedPermissions();
    await this.assignPermissionsToRoles();

    console.log('✅ RBAC seeding completed!');
  }
}
