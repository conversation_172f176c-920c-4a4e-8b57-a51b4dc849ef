import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import * as entities from '../database/entities';

export const getDatabaseConfig = (
  configService: ConfigService,
): TypeOrmModuleOptions => ({
  type: 'postgres',
  host: configService.get('DB_HOST'),
  port: configService.get('DB_PORT'),
  username: configService.get('DB_USERNAME'),
  password: configService.get('DB_PASSWORD'),
  database: configService.get('DB_NAME'),
  entities: Object.values(entities),
  synchronize: false, // Tắt synchronize, dùng migration
  migrations: ['dist/database/migrations/*.js'],
  migrationsRun: true, // Tự động chạy migration khi start app
  logging: configService.get('NODE_ENV') === 'development',
});
